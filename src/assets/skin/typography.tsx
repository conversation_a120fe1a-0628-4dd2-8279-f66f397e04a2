import {TextStyle} from 'react-native';

export class TypoSkin {
  // Regular styles
   static regular0: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 14,
      fontSize: 10,
      fontWeight: '400',
      color: '#18181B',
    } 

   static regular1: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 16,
      fontSize: 12,
      fontWeight: '400',
      color: '#18181B',
    } 

   static regular2: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 22,
      fontSize: 14,
      fontWeight: '400',
      color: '#18181B',
    } 

   static regular3: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 24,
      fontSize: 16,
      fontWeight: '400',
      color: '#18181B',
    } 

   static regular4: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 28,
      fontSize: 20,
      fontWeight: '400',
      color: '#18181B',
    } 

   static // Medium styles
    medium1: {
      fontFamily: 'NotoSansJP-Medium',
      lineHeight: 16,
      fontSize: 12,
      fontWeight: '500',
      color: '#18181B',
    } 

   static medium2: {
      fontFamily: 'NotoSansJP-Medium',
      lineHeight: 22,
      fontSize: 14,
      fontWeight: '500',
      color: '#18181B',
    } 

   static medium3: {
      fontFamily: 'NotoSansJP-Medium',
      lineHeight: 24,
      fontSize: 16,
      fontWeight: '500',
      color: '#18181B',
    } 

   static medium4: {
      fontFamily: 'NotoSansJP-Medium',
      lineHeight: 28,
      fontSize: 20,
      fontWeight: '500',
      color: '#18181B',
    } 

   static // Bold styles
    bold1: {
      fontFamily: 'NotoSansJP-Bold',
      lineHeight: 16,
      fontSize: 12,
      fontWeight: '700',
      color: '#18181B',
    } 

   static bold2: {
      fontFamily: 'NotoSansJP-Bold',
      lineHeight: 22,
      fontSize: 14,
      fontWeight: '700',
      color: '#18181B',
    } 

   static bold3: {
      fontFamily: 'NotoSansJP-Bold',
      lineHeight: 24,
      fontSize: 16,
      fontWeight: '700',
      color: '#18181B',
    } 

   static bold4: {
      fontFamily: 'NotoSansJP-Bold',
      lineHeight: 28,
      fontSize: 20,
      fontWeight: '700',
      color: '#18181B',
    } 

   static // Heading styles
    heading1: {
      fontFamily: 'NotoSansJP-Bold',
      lineHeight: 68,
      fontSize: 56,
      fontWeight: '700',
      color: '#18181B',
    } 

   static heading2: {
      fontFamily: 'NotoSansJP-Bold',
      lineHeight: 56,
      fontSize: 46,
      fontWeight: '700',
      color: '#18181B',
    } 

   static heading3: {
      fontFamily: 'NotoSansJP-SemiBold',
      lineHeight: 46,
      fontSize: 38,
      fontWeight: '600',
      color: '#18181B',
    } 

   static heading4: {
      fontFamily: 'NotoSansJP-SemiBold',
      lineHeight: 38,
      fontSize: 30,
      fontWeight: '600',
      color: '#18181B',
    } 

   static heading5: {
      fontFamily: 'NotoSansJP-SemiBold',
      fontSize: 22,
      fontWeight: '600',
      color: '#18181B',
    } 

   static heading6: {
      fontFamily: 'NotoSansJP-Medium',
      lineHeight: 28,
      fontSize: 20,
      fontWeight: '500',
      color: '#18181B',
    } 

   static // Body styles
    body1: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 28,
      fontSize: 18,
      fontWeight: '400',
      color: '#18181B',
    } 

   static body2: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 24,
      fontSize: 16,
      fontWeight: '400',
      color: '#18181B',
    } 

   static body3: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 22,
      fontSize: 14,
      fontWeight: '400',
      color: '#18181B',
    } 

   static // Button styles
    buttonText1: {
      fontFamily: 'NotoSansJP-Medium',
      lineHeight: 24,
      fontSize: 16,
      fontWeight: '500',
      color: '#18181B',
    } 

   static buttonText2: {
      fontFamily: 'NotoSansJP-Medium',
      lineHeight: 22,
      fontSize: 14,
      fontWeight: '500',
      color: '#18181B',
    } 
   static buttonText5: {
      fontFamily: 'NotoSansJP-Medium',
      lineHeight: 20,
      fontSize: 12,
      fontWeight: '500',
      color: '#18181B',
    } 

   static   caption: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 16,
      fontSize: 12,
      fontWeight: '400',
      color: '#18181B',
    } 

   static subtitle1: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 26,
      fontSize: 18,
      fontWeight: '400',
      color: '#18181B',
    } 

   static subtitle2: {
      fontFamily: 'NotoSansJP-Regular',
      lineHeight: 24,
      fontSize: 16,
      fontWeight: '400',
      color: '#18181B',
    } 

   static // Light styles
    light1: {
      fontFamily: 'NotoSansJP-Light',
      lineHeight: 16,
      fontSize: 12,
      fontWeight: '300',
      color: '#18181B',
    } 

   static light2: {
      fontFamily: 'NotoSansJP-Light',
      lineHeight: 22,
      fontSize: 14,
      fontWeight: '300',
      color: '#18181B',
    } 

   static light3: {
      fontFamily: 'NotoSansJP-Light',
      lineHeight: 24,
      fontSize: 16,
      fontWeight: '300',
      color: '#18181B',
    } 
}
